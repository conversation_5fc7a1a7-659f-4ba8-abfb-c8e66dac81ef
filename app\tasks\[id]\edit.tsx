import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
  Platform
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  Calendar,
  AlertCircle,
  User,
  MapPin,
  Save,
  ArrowLeft,
} from 'lucide-react-native';

import { colors } from '@/constants/colors';
import { useTranslation } from '@/hooks/useTranslation';
import { useTaskStore } from '@/store/task-store';
import { Task, TaskPriority, TaskRecurrence } from '@/types/task';
import { firestore } from '@/config/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { useFarmStore } from '@/store/farm-store';
import { getEmployeesByFarm } from '@/services/employee-service';
import GenericDropdown from '@/components/GenericDropdown';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useAuthStore } from '@/store/auth-store';

export default function EditTaskScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { updateTaskDetails } = useTaskStore();
  const { farms } = useFarmStore();
  const { user } = useAuthStore();
  const themedColors = useThemeColors();

  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Form fields
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [farmId, setFarmId] = useState('');
  const [farmName, setFarmName] = useState('');
  const [assigneeId, setAssigneeId] = useState('');
  const [assigneeName, setAssigneeName] = useState('');
  const [dueDate, setDueDate] = useState(new Date());
  const [priority, setPriority] = useState<TaskPriority>(TaskPriority.MEDIUM);
  const [notes, setNotes] = useState('');
  const [recurrence, setRecurrence] = useState<TaskRecurrence>(TaskRecurrence.NONE);

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [employees, setEmployees] = useState<any[]>([]);
  const [loadingEmployees, setLoadingEmployees] = useState(false);

  const styles = getStyles(themedColors);

  useEffect(() => {
    if (id) {
      loadTask(id);
    }
  }, [id]);

  useEffect(() => {
    if (farmId) {
      loadEmployees();
    } else {
      setEmployees([]);
      // Don't clear assignee data when farmId is empty during initial load
      if (!loading) {
        setAssigneeId('');
        setAssigneeName('');
      }
    }
  }, [farmId, loading]);

  const loadTask = async (taskId: string) => {
    try {
      setLoading(true);
      const taskDoc = await getDoc(doc(firestore, 'tasks', taskId));

      if (taskDoc.exists()) {
        const taskData = { id: taskDoc.id, ...taskDoc.data() } as Task;
        setTask(taskData);

        // Populate form fields
        setTitle(taskData.title);
        setDescription(taskData.description);
        setFarmId(taskData.farmId);
        setFarmName(taskData.farmName);
        setAssigneeId(taskData.assignedTo);
        setAssigneeName(taskData.assigneeName);
        setDueDate(new Date(taskData.due_date));
        setPriority(taskData.priority);
        setNotes(taskData.notes || '');
        setRecurrence(taskData.recurrence);
      } else {
        Alert.alert(t('common.error'), t('tasks.notFound'));
        router.back();
      }
    } catch (error) {
      console.error('Error loading task:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
    } finally {
      setLoading(false);
    }
  };

  const loadEmployees = async () => {
    if (!farmId) return;

    setLoadingEmployees(true);
    try {
      const farmEmployees = await getEmployeesByFarm(farmId);
      setEmployees(farmEmployees);

      // If we have a task with assignedTo but no assigneeName,
      // try to find the assignee name from the loaded employees
      if (task && task.assignedTo && !assigneeName && farmEmployees.length > 0) {
        const assignee = farmEmployees.find(emp => emp.id === task.assignedTo);
        if (assignee) {
          setAssigneeName(assignee.name);
        }
      }
    } catch (error) {
      console.error('Error loading employees:', error);
    } finally {
      setLoadingEmployees(false);
    }
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      const updates: Partial<Task> = {
        title,
        description,
        farmId,
        farmName,
        assignedTo: assigneeId,
        assigneeName,
        due_date: dueDate.getTime(),
        priority,
        notes,
        recurrence,
      };

      await updateTaskDetails(id!, updates, user?.id, user?.role);

      Alert.alert(
        t('common.success'),
        t('tasks.updateSuccess'),
        [
          {
            text: t('common.ok'),
            onPress: () => {
              router.replace(`/tasks/${id}`);
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error updating task:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
    } finally {
      setSaving(false);
    }
  };

  const validateForm = (): boolean => {
    if (!title.trim()) {
      Alert.alert(t('common.error'), t('tasks.titleRequired'));
      return false;
    }

    if (!description.trim()) {
      Alert.alert(t('common.error'), t('tasks.descriptionRequired'));
      return false;
    }

    if (!farmId) {
      Alert.alert(t('common.error'), t('tasks.farmRequired'));
      return false;
    }

    if (!assigneeId) {
      Alert.alert(t('common.error'), t('tasks.assigneeRequired'));
      return false;
    }

    return true;
  };

  const handleFarmSelect = (selectedFarmId: string) => {
    const selectedFarm = farms.find(farm => farm.id === selectedFarmId);
    if (selectedFarm) {
      setFarmId(selectedFarm.id);
      setFarmName(selectedFarm.name);
      // Reset assignee when farm changes
      setAssigneeId('');
      setAssigneeName('');
    }
  };

  const handleAssigneeSelect = (selectedAssigneeId: string) => {
    const selectedEmployee = employees.find(emp => emp.id === selectedAssigneeId);
    if (selectedEmployee) {
      setAssigneeId(selectedEmployee.id);
      setAssigneeName(selectedEmployee.name);
    }
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setDueDate(selectedDate);
    }
  };

  const recurrenceOptions = useMemo(() => [
    { id: TaskRecurrence.NONE, label: t('tasks.recurrenceValues.none'), icon: <AlertCircle size={20} color={colors.textSecondary} /> },
    { id: TaskRecurrence.DAILY, label: t('tasks.recurrenceValues.daily'), icon: <AlertCircle size={20} color={colors.primary} /> },
    { id: TaskRecurrence.WEEKLY, label: t('tasks.recurrenceValues.weekly'), icon: <AlertCircle size={20} color={colors.primary} /> },
    { id: TaskRecurrence.MONTHLY, label: t('tasks.recurrenceValues.monthly'), icon: <AlertCircle size={20} color={colors.primary} /> },
  ], [t]);
  const priorityOptions = useMemo(() => [
    { id: TaskPriority.LOW, label: t('tasks.priorityValue.low'), icon: <AlertCircle size={20} color={colors.success} /> },
    { id: TaskPriority.MEDIUM, label: t('tasks.priorityValue.medium'), icon: <AlertCircle size={20} color={colors.warning} /> },
    { id: TaskPriority.HIGH, label: t('tasks.priorityValue.high'), icon: <AlertCircle size={20} color={colors.error} /> },
  ], [t]);
  const farmOptions = useMemo(() => farms.map(farm => ({
    id: farm.id,
    label: farm.name,
    icon: <MapPin size={20} color={colors.primary} />
  })), [farms]);

  const employeeOptions = useMemo(() => employees.map(emp => ({
    id: emp.id,
    label: emp.name,
    imageUri: emp.photo ? { uri: emp.photo } : undefined,
    icon: !emp.photo ? (
      <View style={styles.employeePhotoPlaceholder}>
        <Text style={styles.employeePhotoPlaceholderText}>
          {emp.name.substring(0, 1).toUpperCase()}
        </Text>
      </View>
    ) : undefined
  })), [employees, styles]);


  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }

  if (!task) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('tasks.notFound')}</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={20} color={colors.primary} />
          <Text style={styles.backButtonText}>{t('common.back')}</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: t('tasks.editTask'),
          headerBackTitle: t('common.back')
        }}
      />

      <ScrollView style={styles.scrollView}>
        {/* Title */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{t('tasks.title')} *</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder={t('tasks.titlePlaceholder')}
            placeholderTextColor={colors.textLight}
          />
        </View>

        {/* Description */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{t('tasks.description')} *</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder={t('tasks.descriptionPlaceholder')}
            placeholderTextColor={colors.textLight}
            multiline
            numberOfLines={4}
          />
        </View>

        {/* Farm Selection */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{t('farms.farm')} *</Text>
          <GenericDropdown
            items={farmOptions}
            value={farmId}
            onSelect={handleFarmSelect}
            placeholder={t('farms.selectFarm')}
            renderIcon={<MapPin size={20} color={colors.primary} />}
          />
        </View>

        {/* Assignee Selection */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{t('tasks.assignTo')} *</Text>
          <GenericDropdown
            items={employeeOptions}
            value={assigneeId}
            onSelect={handleAssigneeSelect}
            placeholder={loadingEmployees ? t('common.loading') : t('tasks.selectAssignee')}
            renderIcon={<User size={20} color={colors.primary} />}
            disabled={loadingEmployees || employees.length === 0}
          />
        </View>

        {/* Due Date */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{t('tasks.dueDate')} *</Text>
          <TouchableOpacity
            style={styles.dateButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Calendar size={20} color={colors.primary} />
            <Text style={styles.dateButtonText}>
              {dueDate.toLocaleDateString()}
            </Text>
          </TouchableOpacity>

          {showDatePicker && (
            <DateTimePicker
              value={dueDate}
              mode="date"
              display="default"
              onChange={handleDateChange}
              minimumDate={new Date()}
            />
          )}
        </View>

        {/* Priority */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{t('tasks.priority')}</Text>
          <GenericDropdown
            items={priorityOptions}
            value={priority}
            onSelect={(value) => setPriority(value as TaskPriority)}
            placeholder={t('tasks.selectPriority')}
            renderIcon={<AlertCircle size={20} color={colors.primary} />}
          />
        </View>

        {/* Recurrence */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{t('tasks.recurrence')}</Text>
          <GenericDropdown
            items={recurrenceOptions}
            value={recurrence}
            onSelect={(value) => setRecurrence(value as TaskRecurrence)}
            placeholder={t('tasks.selectRecurrence')}
            renderIcon={<Calendar size={20} color={colors.primary} />}
          />
        </View>

        {/* Notes */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{t('tasks.notes')}</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={notes}
            onChangeText={setNotes}
            placeholder={t('tasks.notesPlaceholder')}
            placeholderTextColor={colors.textLight}
            multiline
            numberOfLines={3}
          />
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, saving && styles.disabledButton]}
          onPress={handleSave}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size={20} color={colors.white} />
          ) : (
            <Save size={20} color={colors.white} />
          )}
          <Text style={styles.saveButtonText}>
            {saving ? t('common.updating') : t('common.update')}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: themedColors.background,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: themedColors.background,
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: colors.error,
    textAlign: 'center',
    marginBottom: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  backButtonText: {
    color: colors.primary,
    fontSize: 16,
    marginLeft: 5,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: themedColors.text,
    backgroundColor: themedColors.card,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    padding: 12,
    backgroundColor: themedColors.card,
  },
  dateButtonText: {
    fontSize: 16,
    color: themedColors.text,
    marginLeft: 10,
  },
  saveButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 20,
    marginBottom: 30,
  },
  disabledButton: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  employeePhotoPlaceholder: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: themedColors.primary + '20',
    alignItems: 'center',
    justifyContent: 'center'
  },
  employeePhotoPlaceholderText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: themedColors.primary
  },
});
