import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MapPin, Cat, Users } from 'lucide-react-native';
import { Farm, FarmStatus } from '@/types/farm';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';

interface FarmCardProps {
  farm: Farm;
  onPress: (farmId: string) => void;
  cardStyle?: object;
}

const FarmCard: React.FC<FarmCardProps> = ({ farm, onPress, cardStyle }) => {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);

  const handlePress = () => {
    try {
      console.log('FarmCard: handlePress called for farm:', farm?.id, farm?.name);

      if (!farm || !farm.id) {
        console.error('FarmCard: Invalid farm data', farm);
        return;
      }

      console.log('FarmCard: Calling onPress with farmId:', farm.id);
      onPress(farm.id);
    } catch (error) {
      console.error('FarmCard: Error in onPress handler', error);
    }
  };

  // Safety check for farm data
  if (!farm) {
    console.error('FarmCard: No farm data provided');
    return null;
  }

  return (
    // The main card container already handles padding and overall layout
    <TouchableOpacity
      style={[styles.farmCard, cardStyle, { backgroundColor: themedColors.card }]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.farmCardContent}>
        <View style={[
          styles.farmCardHeader,
          language === 'ur' && styles.urduFarmCardHeader // Apply specific RTL header styles
        ]}>
          {/* Farm Name */}
          <Text style={[
            styles.farmName,
            language === 'ur' && styles.urduText // Apply RTL text alignment
          ]}>{farm.name}</Text>
          <View style={[
            styles.statusBadge,
            farm.status === FarmStatus.ACTIVE && styles.statusActive,
            farm.status === FarmStatus.INACTIVE && styles.statusInactive,
            farm.status === FarmStatus.PENDING && styles.statusPending,
            farm.status === FarmStatus.COMPLETED && styles.statusCompleted,
            language === 'ur' && styles.urduStatusBadge // Adjust margin for RTL
          ]}>
            <Text style={[
              styles.statusText,
              language === 'ur' && styles.urduText // Apply RTL text alignment
            ]}>
              {t(`farms.status${farm.status.charAt(0).toUpperCase() + farm.status.slice(1)}`)}
            </Text>
          </View>
        </View>

        <View style={[
          styles.farmLocation,
          language === 'ur' && { flexDirection: 'row-reverse' }
          // No need to adjust justifyContent here, default is flex-start which works for both LTR/RTL row
        ]}>
          <View style={[
            styles.iconContainer,
            language === 'ur' && styles.urduIconContainer // Adjust margin for RTL
          ]}>
            <MapPin size={16} color={themedColors.primary} strokeWidth={1.5} />
          </View>
          <Text style={[ // Text alignment handled by urduText style
            styles.locationText,
            language === 'ur' && styles.urduText
          ]}>{farm.location}</Text>
        </View>

        <View style={styles.farmStats}>
          <View style={[
            styles.statItem,
            language === 'ur' && { flexDirection: 'row-reverse' }
            // No need to adjust justifyContent here, default is flex-start which works for both LTR/RTL row
          ]}>
            <View style={[
              styles.iconContainer,
              language === 'ur' && styles.urduIconContainer // Adjust margin for RTL
            ]}>
              <Cat size={16} color={themedColors.primary} strokeWidth={1.5} />
            </View>
            <Text style={[ // Text alignment handled by urduText style
              styles.statText,
              language === 'ur' && styles.urduText
            ]}>
              {t('farms.animals')}: {farm.animalCount || 0}
            </Text>
          </View>
          <View style={[
            styles.statItem,
            language === 'ur' && { flexDirection: 'row-reverse' }
            // No need to adjust justifyContent here, default is flex-start which works for both LTR/RTL row
          ]}>
            <View style={[
              styles.iconContainer,
              language === 'ur' && styles.urduIconContainer // Adjust margin for RTL
            ]}>
              <Users size={16} color={themedColors.primary} strokeWidth={1.5} />
            </View>
            <Text style={[ // Text alignment handled by urduText style
              styles.statText,
              language === 'ur' && styles.urduText
            ]}>
              {t('farms.staff')}: {farm.staffCount || 0}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  farmCard: {
    // backgroundColor will be set dynamically
    borderRadius: 12,
    marginBottom: 16,
    // Theme-aware shadow properties
    shadowColor: themedColors.isDarkMode ? '#000' : '#000',
    shadowOffset: { width: 0, height: 1 }, // Adjusted for a more subtle shadow
    shadowOpacity: themedColors.isDarkMode ? 0.2 : 0.08, // Adjusted opacity
    shadowRadius: themedColors.isDarkMode ? 3 : 2, // Adjusted radius
    elevation: themedColors.isDarkMode ? 1 : 2,
    borderWidth: themedColors.isDarkMode ? 1 : 0, // Optional: add border in dark mode
    borderColor: themedColors.isDarkMode ? themedColors.border : 'transparent',
    overflow: 'hidden',
    maxHeight: 400
  },
  farmCardContent: {
    padding: 16,
  },
  farmCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  urduFarmCardHeader: {
    flexDirection: 'row-reverse', // Badge then Name (visually)
    justifyContent: 'flex-start', // Align the group to the right (start of reversed axis)
  },
  farmName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text, // Use themed text color
    flex: 1,
    textAlign: 'left', // Default LTR alignment
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
  },
  urduStatusBadge: {
    marginLeft: 0, // Remove left margin in RTL
    marginRight: 8, // Add right margin in RTL
  },
  statusActive: {
    backgroundColor: '#10B981',
  },
  statusInactive: {
    backgroundColor: '#6B7280',
  },
  statusPending: {
    backgroundColor: '#F59E0B',
  },
  statusCompleted: {
    backgroundColor: '#3B82F6',
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  farmLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 25,
    height: 25,
    borderRadius: 12.5, // Keep it circular
    backgroundColor: themedColors.primaryLight, // Use themed primary light color
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  urduIconContainer: {
    marginRight: 0, // Remove right margin in RTL
    marginLeft: 8, // Add left margin in RTL
  },
  locationText: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  farmStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    marginLeft: 4,
    marginRight: 0, // Default LTR margin
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  urduText: {
    textAlign: 'right',
    writingDirection: 'rtl',
  },
});

export default FarmCard;
