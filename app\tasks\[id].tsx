import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle2,
  MapPin,
  User,
  FileText,
  Edit,
  Trash2,
  Repeat,
  ArrowLeft,
} from 'lucide-react-native';

import { colors } from '@/constants/colors';
import { useTranslation } from '@/hooks/useTranslation';
import { useTaskStore } from '@/store/task-store';
import { Task, TaskStatus, TaskPriority, TaskRecurrence } from '@/types/task';
import { firestore } from '@/config/firebase';
import { doc, getDoc } from 'firebase/firestore';

import {formatDate} from '@/utils/date-utils';
import { useAuthStore } from '@/store/auth-store';

export default function TaskDetailScreen() {
  const { t,language } = useTranslation();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { updateTaskStatus } = useTaskStore();
   const { user } = useAuthStore();
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';
  useEffect(() => {
    if (id) {
      loadTask(id);
    }
  }, [id]);
  
  const loadTask = async (taskId: string) => {
    try {
      setLoading(true);
      const taskDoc = await getDoc(doc(firestore, 'tasks', taskId));
      
      if (taskDoc.exists()) {
        setTask({ id: taskDoc.id, ...taskDoc.data() } as Task);
      } else {
        Alert.alert(t('common.error'), t('tasks.notFound'));
        router.back();
      }
    } catch (error) {
      console.error('Error loading task:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
    } finally {
      setLoading(false);
    }
  };
  
  const handleMarkComplete = async () => {
    if (!task || !task.id) return;
    
    Alert.alert(
      t('tasks.markComplete'),
      t('tasks.markCompleteConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.confirm'),
          onPress: async () => {
            try {
              setUpdating(true);
              await updateTaskStatus(task.id!, TaskStatus.COMPLETED);
              // Reload the task to get the updated data
              await loadTask(task.id!);
              router.replace('/(tabs)/tasks');
            } catch (error) {
              console.error('Error marking task as complete:', error);
              Alert.alert(t('common.error'), t('common.errorOccurred'));
            } finally {
              setUpdating(false);
            }
          },
        },
      ]
    );
  };
  
  const handleReopenTask = async () => {
    if (!task || !task.id) return;
    
    Alert.alert(
      t('tasks.reopenTask'),
      t('tasks.reopenTaskConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.confirm'),
          onPress: async () => {
            try {
              setUpdating(true);
              await updateTaskStatus(task.id!, TaskStatus.PENDING);
              // Reload the task to get the updated data
              await loadTask(task.id!);
              // router.replace('/(tabs)/tasks');
            } catch (error) {
              console.error('Error reopening task:', error);
              Alert.alert(t('common.error'), t('common.errorOccurred'));
            } finally {
              setUpdating(false);
            }
          },
        },
      ]
    );
  };
  
  const handleEditTask = () => {
    if (!task || !task.id) return;
    router.push(`/tasks/${task.id}/edit`);
  };
  
  const handleDeleteTask = async () => {
    if (!task || !task.id) return;
    
    Alert.alert(
      t('tasks.deleteTask'),
      t('tasks.deleteTaskConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setUpdating(true);
              await useTaskStore.getState().removeTask(task.id!);
              router.back();
            } catch (error) {
              console.error('Error deleting task:', error);
              Alert.alert(t('common.error'), t('common.errorOccurred'));
              setUpdating(false);
            }
          },
        },
      ]
    );
  };
  
  // Get priority color
  const getPriorityColor = (priority: TaskPriority): string => {
    switch (priority) {
      case TaskPriority.HIGH:
        return colors.error;
      case TaskPriority.MEDIUM:
        return colors.warning;
      case TaskPriority.LOW:
        return colors.success;
      default:
        return colors.primary;
    }
  };
  
  // Get priority label
  const getPriorityLabel = (priority: TaskPriority): string => {
    switch (priority) {
      case TaskPriority.HIGH:
        return t('tasks.priority.high');
      case TaskPriority.MEDIUM:
        return t('tasks.priority.medium');
      case TaskPriority.LOW:
        return t('tasks.priority.low');
      default:
        return '';
    }
  };
  
  // Get recurrence label
  const getRecurrenceLabel = (recurrence: TaskRecurrence): string => {
    switch (recurrence) {
      case TaskRecurrence.DAILY:
        return t('tasks.recurrence.daily');
      case TaskRecurrence.WEEKLY:
        return t('tasks.recurrence.weekly');
      case TaskRecurrence.MONTHLY:
        return t('tasks.recurrence.monthly');
      default:
        return t('tasks.recurrence.none');
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }
  
  if (!task) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('tasks.notFound')}</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={20} color={colors.primary} />
          <Text style={styles.backButtonText}>{t('common.back')}</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }
  
  const isCompleted = task.status === TaskStatus.COMPLETED;
  const isOverdue = !isCompleted && task.due_date < Date.now();
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: t('tasks.taskDetails'),
          headerBackTitle: t('common.back')
        }}
      />
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.title}>{task.title}</Text>
          
          <View style={styles.statusContainer}>
            {isCompleted ? (
              <View style={[styles.statusBadge, styles.completedBadge]}>
                <CheckCircle2 size={16} color={colors.success} />
                <Text style={styles.completedStatusText}>{t('tasks.completed')}</Text>
              </View>
            ) : isOverdue ? (
              <View style={[styles.statusBadge, styles.overdueBadge]}>
                <AlertCircle size={16} color={colors.error} />
                <Text style={styles.overdueStatusText}>{t('tasks.overdue')}</Text>
              </View>
            ) : (
              <View style={styles.statusBadge}>
                <Clock size={16} color={colors.primary} />
                <Text style={styles.statusText}>{t('tasks.pending')}</Text>
              </View>
            )}
          </View>
        </View>
        
        {task.description && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('tasks.description')}</Text>
            <Text style={styles.descriptionText}>{task.description}</Text>
          </View>
        )}
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('tasks.details')}</Text>
          
          <View style={styles.detailRow}>
            <View style={styles.detailIconContainer}>
              <MapPin size={20} color={colors.primary} />
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>{t('farms.farm')}</Text>
              <Text style={styles.detailValue}>{task.farmName}</Text>
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.detailIconContainer}>
              <User size={20} color={colors.primary} />
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>{t('tasks.assignedTo')}</Text>
              <Text style={styles.detailValue}>{task.assigneeName}</Text>
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.detailIconContainer}>
              <Calendar size={20} color={colors.primary} />
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>{t('tasks.dueDate')}</Text>
              <Text
                style={[
                  styles.detailValue,
                  isOverdue && !isCompleted && styles.overdueText
                ]}
              >
                {formatDate(task.due_date, locale)}
              </Text>
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.detailIconContainer}>
              <AlertCircle size={20} color={getPriorityColor(task.priority)} />
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>{t('tasks.priority')}</Text>
              <Text 
                style={[
                  styles.detailValue,
                  { color: getPriorityColor(task.priority) }
                ]}
              >
                {t('tasks.priorities.' + getPriorityLabel(task.priority)).toLowerCase()}
              </Text>
            </View>
          </View>
          
          {task.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>{t('tasks.notes')}</Text>
              <Text style={styles.notesText}>{task.notes}</Text>
            </View>
          )}
        </View>
        
        {!isCompleted && (
          <TouchableOpacity
            style={[styles.completeButton, updating && styles.disabledButton]}
            onPress={handleMarkComplete}
            disabled={updating || isCompleted}
          >
            {updating ? (
              <ActivityIndicator size={20} color={colors.white} />
            ) : (
              <CheckCircle2 size={20} color={colors.white} />
            )}
            <Text style={styles.completeButtonText}>
              {updating ? t('common.updating') : t('tasks.markAsComplete')}
            </Text>
          </TouchableOpacity>
        )}
        
        <View style={styles.actionButtons}>
        {!isCompleted && user?.role !== 'caretaker' &&  <TouchableOpacity
            style={[styles.actionButton, styles.editButton]}
            onPress={handleEditTask}
            disabled={updating}
          >
            <Edit size={20} color={colors.white} />
            <Text style={styles.actionButtonText}>
              {t('common.edit')}
            </Text>
          </TouchableOpacity>}

         {user?.role !== 'caretaker' && <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={handleDeleteTask}
            disabled={updating}
          >
            <Trash2 size={20} color={colors.white} />
            <Text style={styles.actionButtonText}>
              {t('common.delete')}
            </Text>
          </TouchableOpacity>}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: colors.error,
    textAlign: 'center',
    marginBottom: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  backButtonText: {
    color: colors.primary,
    fontSize: 16,
    marginLeft: 5,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 10,
  },
  statusContainer: {
    flexDirection: 'row',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryLight,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 16,
  },
  statusText: {
    color: colors.primary,
    fontWeight: '500',
    marginLeft: 5,
  },
  completedBadge: {
    backgroundColor: colors.successLight,
  },
  completedStatusText: {
    color: colors.success,
    fontWeight: '500',
    marginLeft: 5,
  },
  overdueBadge: {
    backgroundColor: colors.errorLight,
  },
  overdueStatusText: {
    color: colors.error,
    fontWeight: '500',
    marginLeft: 5,
  },
  section: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  descriptionText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  detailIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.highlight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  detailContent: {
    flex: 1,
    justifyContent: 'center',
  },
  detailLabel: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    color: colors.text,
    fontWeight: '500',
  },
  overdueText: {
    color: colors.error,
  },
  notesContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: colors.highlight,
    borderRadius: 8,
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 6,
  },
  notesText: {
    fontSize: 15,
    color: colors.text,
    lineHeight: 22,
  },
  completeButton: {
    backgroundColor: colors.success,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  disabledButton: {
    opacity: 0.6,
  },
  completeButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    marginBottom: 30,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  editButton: {
    backgroundColor: colors.primary,
  },
  deleteButton: {
    backgroundColor: colors.error,
  },
  actionButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});






