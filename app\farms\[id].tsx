import React, { useEffect, useState, Suspense } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack, useFocusEffect } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
// import { colors } from '@/constants/colors';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook
import { ErrorBoundary } from '@/app/error-boundary';

import { useFarmStore } from '@/store/farm-store';
import { useAnimalStore } from '@/store/animal-store';
import { useAuthStore } from '@/store/auth-store';
import { useTranslation } from '@/hooks/useTranslation';
import { Farm } from '@/types/farm';
import { Animal } from '@/types/animal';
import { getEmployeesByFarm, Employee, syncFarmStaffCount, getEmployeeById } from '@/services/employee-service';
import { getTotalExpensesByFarm } from '@/services/expense-service'; // Corrected path if needed
import { MapPin, Edit, Trash2, Users, Cat, Plus, DollarSign, Milk } from 'lucide-react-native';
import LoadingIndicator from '@/components/LoadingIndicator';
import { AnimalsTab, StaffTab, ExpensesTab, MilkingTab } from '@/components/FarmDetails';
import { collection, query, getDocs, doc, getDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { getCurrencySymbol } from '@/utils/currency-utils';
import { logMemoryUsage } from '@/utils/memory-utils';

export default function FarmDetailScreen() {
  const { t, language } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { user } = useAuthStore();

  // Log memory usage on component mount
  React.useEffect(() => {
    logMemoryUsage(`FarmDetailScreen mounted - ID: ${id}`);
  }, [id]);
  const { getFarm, refreshFarm, deleteFarm, isLoading: farmLoading, farms } = useFarmStore();
  const { fetchAnimals, fetchAnimalsByFarm, isLoading: animalsLoading } = useAnimalStore();
  const themedColors = useThemeColors(); // Use the theme hook

  // Early validation checks
  React.useEffect(() => {
    // Check if ID is valid
    if (!id || id === 'undefined' || id === 'null') {
      console.error('FarmDetailScreen: Invalid farm ID', id);
      Alert.alert(t('common.error'), t('common.invalidFarmId') || 'Invalid farm ID', [
        { text: t('common.ok'), onPress: () => router.replace('/(tabs)/farms') }
      ]);
      return;
    }

    // Check user permissions
    if (!user || !(user.role === 'owner' || user.role === 'admin' || user.role === 'caretaker')) {
      console.error('FarmDetailScreen: Permission denied for user', user);
      Alert.alert(t('common.permissionDenied'), t('common.noAccessToThisFeature'), [
        { text: t('common.ok'), onPress: () => router.replace('/(tabs)') }
      ]);
      return;
    }
  }, [id, user, router, t]);

  const styles = getStyles(themedColors); // Generate styles with themed colors
  const [farm, setFarm] = useState<Farm | undefined>(getFarm(id));
  const [farmAnimals, setFarmAnimals] = useState<Animal[]>([]);
  const [farmStaff, setFarmStaff] = useState<Employee[]>([]);
  const [activeTab, setActiveTab] = useState('animals');

  // Log memory usage when tab changes
  React.useEffect(() => {
    logMemoryUsage(`FarmDetailScreen: Tab changed to ${activeTab}`);
  }, [activeTab]); // 'animals' or 'staff'
  const [isLoadingStaff, setIsLoadingStaff] = useState(false);
  const [totalExpense, setTotalExpense] = useState<number | null>(null);
  const [isLoadingExpense, setIsLoadingExpense] = useState(false);
  const [expensesByCategory, setExpensesByCategory] = useState<{ category: string, amount: number }[]>([]);
  const [isLoadingExpenseChart, setIsLoadingExpenseChart] = useState(false);

  const fetchExpensesByCategory = async () => {
    setIsLoadingExpenseChart(true);
    try {
      const expensesRef = collection(firestore, 'farms', id, 'expenses');
      const q = query(expensesRef);
      const querySnapshot = await getDocs(q);

      // Group expenses by category
      const expensesByCategory: Record<string, number> = {};

      querySnapshot.forEach((doc) => {
        const expense = doc.data();
        const category = expense.category || 'Other';
        const amount = expense.amount || 0;

        if (!expensesByCategory[category]) {
          expensesByCategory[category] = 0;
        }

        expensesByCategory[category] += amount;
      });

      // Convert to array format needed for charts
      const result = Object.entries(expensesByCategory).map(([category, amount]) => ({
        category,
        amount
      }));

      setExpensesByCategory(result);
    } catch (error) {
      // Error fetching expenses by category
    } finally {
      setIsLoadingExpenseChart(false);
    }
  };

  useEffect(() => {
    if (user && farm) {
      // Fetch all animals for the user (needed for other screens)
      fetchAnimals(user.id);

      // Also fetch animals specific to this farm
      const loadFarmData = async () => {
        try {
          logMemoryUsage('FarmDetailScreen: Starting data load');
          setIsLoadingExpense(true); // Start loading expense
          // Load animals
          const animals = await fetchAnimalsByFarm(id);
          setFarmAnimals(animals);

          // Load staff
          setIsLoadingStaff(true);
          try {
            // Get the farm document to get the staff array
            const farmRef = doc(firestore, 'farms', id);
            const farmDoc = await getDoc(farmRef);

            if (farmDoc.exists()) {
              const farmData = farmDoc.data();
              const staffIds = farmData.staff || [];

              // If there are staff IDs, fetch each employee
              if (staffIds.length > 0) {
                const staffPromises = staffIds.map((staffId: string) => getEmployeeById(staffId));
                const staffMembers = await Promise.all(staffPromises);
                setFarmStaff(staffMembers as Employee[]);
              } else {
                setFarmStaff([]);
              }
            } else {
              setFarmStaff([]);
            }
          } catch (staffError) {
            setFarmStaff([]);
          } finally {
            setIsLoadingStaff(false);
          }

          // Load total expenses
          try {

            // Validate that id is a valid farm ID
            if (!id || id === 'undefined') {
              setIsLoadingExpense(false);
              return;
            }

            // Check if this is a valid farm ID
            const farmExists = farms && farms.some(farm => farm.id === id);
            if (!farmExists) {
              setIsLoadingExpense(false);
              return;
            }

            const expenses = await getTotalExpensesByFarm(id);
            setTotalExpense(expenses);
          } catch (expenseError) {
            // Error fetching total expenses
          } finally {
            setIsLoadingExpense(false);
          }
        } catch (error) {
          // Error fetching farm data
        }
      };

      loadFarmData();
    }
  }, [user, farm, id]);

  // Refresh farm animals when returning to this screen
  useFocusEffect(
    React.useCallback(() => {
      const refreshFarmData = async () => {
        if (user && id) {
          try {
            setIsLoadingExpense(true); // Start loading expense
            // Synchronize the farm's staffCount with the actual number of employees
            try {
              await syncFarmStaffCount(id);
            } catch (syncError) {
              // Continue even if synchronization fails
            }

            // Refresh the farm data directly from Firestore
            const updatedFarm = await refreshFarm(id);
            if (updatedFarm) {
              setFarm(updatedFarm);
            }

            // Refresh farm animals
            const animals = await fetchAnimalsByFarm(id);
            setFarmAnimals(animals);

            // Refresh farm staff
            setIsLoadingStaff(true);
            try {
              const staff = await getEmployeesByFarm(id);
              setFarmStaff(staff as Employee[]);
            } catch (staffError) {
              // Error fetching staff
            } finally {
              setIsLoadingStaff(false);
            }


            // Refresh total expenses
            try {
              // Validate that id is a valid farm ID
              if (!id || id === 'undefined') {
                setIsLoadingExpense(false);
                return;
              }

              const expenses = await getTotalExpensesByFarm(id);
              setTotalExpense(expenses);
            } catch (expenseError) {
              // Error fetching total expenses
            } finally {
              setIsLoadingExpense(false); // Finish loading expense
            }
          } catch (error) {
            // Error refreshing farm data
          }
        }
      };

      refreshFarmData();

      // Fetch expense data by category
      fetchExpensesByCategory();

      // No cleanup needed for useFocusEffect
      return () => { };
    }, [id, user, refreshFarm, fetchAnimalsByFarm])
  );

  useEffect(() => {
    // Update farm when it changes in the store
    try {
      if (id && id !== 'undefined' && id !== 'null') {
        const farmData = getFarm(id);
        setFarm(farmData);

        // If farm is not found in store, show error
        if (!farmData && farms.length > 0) {
          console.error('FarmDetailScreen: Farm not found in store', id);
          Alert.alert(t('common.error'), t('common.farmNotFound') || 'Farm not found', [
            { text: t('common.ok'), onPress: () => router.replace('/(tabs)/farms') }
          ]);
        }
      }
    } catch (error) {
      console.error('FarmDetailScreen: Error getting farm from store', error);
    }
  }, [id, getFarm, farms, router, t]);

  // Helper function for formatting currency (you might want this in a utils file)
  const formatCurrency = (amount: number | null | undefined, currencyCode = 'PKR') => {
    if (amount === null || amount === undefined) return '--'; // Or 'Loading...' or similar
    const symbol = getCurrencySymbol(currencyCode);
    return `${symbol} ${amount.toFixed(2)}`; // Adjust decimals as needed
  };
  const handleEdit = () => {
    router.push(`/farms/${id}/edit`);
  };

  const handleDelete = () => {
    Alert.alert(
      t('farms.deleteConfirm'),
      t('common.cannotBeUndone'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          onPress: async () => {
            try {
              await deleteFarm(id);
              router.replace('/(tabs)/farms');
              // Show success message
              Alert.alert(t('common.success'), t('farms.deleteSuccess'));
            } catch (error) {
              Alert.alert(t('common.error'), t('common.errorOccurred'));
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  const handleAddAnimal = () => {
    router.push({
      pathname: '/animals/add',
      params: { farmId: id }
    });
  };

  const handleAddEmployee = () => {
    router.push({
      pathname: '/employees/add',
      params: { farmId: id }
    });
  };

  // Add this function to handle adding an expense
  const handleAddExpense = () => {
    router.push({
      pathname: '/expenses/add',
      params: { farmId: id }
    });
  };

  // Add this function to handle viewing all expenses
  const handleViewAllExpenses = () => {
    router.push({
      pathname: '/expenses/list',
      params: { farmId: id }
    });
  };

  // Add this function to handle adding a milking record
  const handleAddMilking = () => {
    router.push({
      pathname: '/milking/add',
      params: { farmId: id }
    });
  };



  if (farmLoading || animalsLoading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  if (!farm) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{t('common.notFound')}</Text>
      </View>
    );
  }






  return (
    <ErrorBoundary>
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <Stack.Screen
          options={{
            title: t('farms.farmDetails'),
            headerBackTitle: t('common.back'),
          }}
        />
        <View style={styles.header}>

          {/* <View style={styles.}> */}
          <View style={[styles.farmNameContainer, language === 'ur' && { flexDirection: 'row-reverse' }]}>
            <Text style={[
              styles.farmName,
              language === 'ur' && styles.urduText
            ]}>{farm.name}</Text>
            <View style={styles.actionIcons}>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={handleEdit}
              >
                <Edit size={18} color={themedColors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={handleDelete}
              >
                <Trash2 size={18} color={themedColors.error} />
              </TouchableOpacity>
            </View>
          </View>
          <View style={[styles.locationDetailContainer, language === 'ur' && { flexDirection: 'row-reverse' }]}>
            <MapPin size={18} color={themedColors.textSecondary} />
            <Text style={[
              styles.locationDetaillable,
              language === 'ur' && styles.urduText
            ]}>{t('farms.address')}: </Text>
            <Text style={[
              styles.locationDetailText,
              language === 'ur' && styles.urduText
            ]}>{farm.location}</Text>
          </View>
          <View style={[styles.locationDetailContainer, language === 'ur' && { flexDirection: 'row-reverse' }]}>
            <DollarSign size={18} color={themedColors.textSecondary} />
            <Text style={[
              styles.locationDetaillable,
              language === 'ur' && styles.urduText
            ]}>{t('farms.totalExpenses')}: </Text>
            <Text style={[
              styles.locationDetailText,
              language === 'ur' && styles.urduText
            ]}>{isLoadingExpense ? '...' : formatCurrency(totalExpense)}</Text>
          </View>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Cat size={24} color={themedColors.primary} />
            <Text style={styles.statValue}>{farm.animalCount || 0}</Text>
            <Text style={styles.statLabel}>{t('farms.animals')}</Text>
          </View>
          <View style={styles.statCard}>
            <Users size={24} color={themedColors.primary} />
            <Text style={styles.statValue}>{farm.staffCount || 0}</Text>
            <Text style={styles.statLabel}>{t('farms.staff')}</Text>
          </View>
        </View>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'animals' && styles.activeTabButton]}
            onPress={() => setActiveTab('animals')}
          >
            <Cat size={16} color={activeTab === 'animals' ? themedColors.primary : themedColors.textSecondary} />
            <Text style={[styles.tabButtonText, activeTab === 'animals' && styles.activeTabText, { fontSize: 12, marginLeft: 2 }]}>
              {t('farms.animals')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'staff' && styles.activeTabButton]}
            onPress={() => setActiveTab('staff')}
          >
            <Users size={16} color={activeTab === 'staff' ? themedColors.primary : themedColors.textSecondary} />
            <Text style={[styles.tabButtonText, activeTab === 'staff' && styles.activeTabText, { fontSize: 12, marginLeft: 2 }]}>
              {t('farms.staff')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'expenses' && styles.activeTabButton,
              { paddingHorizontal: 6 } // Reduce horizontal padding more
            ]}
            onPress={() => setActiveTab('expenses')}
          >
            <DollarSign size={16} color={activeTab === 'expenses' ? themedColors.primary : themedColors.textSecondary} />
            <Text
              style={[
                styles.tabButtonText,
                activeTab === 'expenses' && styles.activeTabText,
                { fontSize: 12, marginLeft: 2 } // Smaller font and less margin
              ]}
              numberOfLines={1}
            >
              {t('farms.expenses')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'milking' && styles.activeTabButton,
              { paddingHorizontal: 6 } // Reduce horizontal padding
            ]}
            onPress={() => setActiveTab('milking')}
          >
            <Milk size={16} color={activeTab === 'milking' ? themedColors.primary : themedColors.textSecondary} />
            <Text
              style={[
                styles.tabButtonText,
                activeTab === 'milking' && styles.activeTabText,
                { fontSize: 12, marginLeft: 2 } // Smaller font and less margin
              ]}
              numberOfLines={1}
            >
              {t('farms.milking')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        <View style={styles.tabContentContainer}>
          {activeTab === 'animals' && (
            <AnimalsTab
              farmAnimals={farmAnimals || []}
              farm={farm}
              onAddAnimal={handleAddAnimal}
              styles={styles}
            />
          )}
          {activeTab === 'staff' && (
            <StaffTab
              farmStaff={farmStaff || []}
              isLoadingStaff={isLoadingStaff}
              onAddEmployee={handleAddEmployee}
              styles={styles}
            />
          )}
          {activeTab === 'milking' && (
            <MilkingTab
              farm={farm}
              onAddMilking={handleAddMilking}
              styles={styles}
            />
          )}
          {activeTab === 'expenses' && (
            <ExpensesTab
              expensesByCategory={expensesByCategory || []}
              isLoadingExpenseChart={isLoadingExpenseChart}
              onAddExpense={handleAddExpense}
              onViewAllExpenses={handleViewAllExpenses}
              styles={styles}
            />
          )}
        </View>

        {/* Add Button */}
        <TouchableOpacity
          style={styles.addButton}
          onPress={
            activeTab === 'animals'
              ? handleAddAnimal
              : activeTab === 'staff'
                ? handleAddEmployee
                : activeTab === 'expenses'
                  ? handleAddExpense
                  : handleAddMilking
          }
        >
          <Plus size={24} color="white" />
        </TouchableOpacity>
      </SafeAreaView>
    </ErrorBoundary>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  header: {
    backgroundColor: themedColors.card,
    padding: 16,
    paddingTop: 12, // Adjusted padding for better spacing
    paddingBottom: 8, // Reduced bottom padding for better spacing
  },
  headerTitle: {
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  headerLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationText: {
    marginLeft: 4,
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  divider: {
    height: 1,
    backgroundColor: themedColors.border,
    marginBottom: 8, // Reduced margin since we removed the title
  },
  farmNameContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  farmName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
    flex: 1,
  },
  actionIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 12,
  },
  checkIcon: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: '#10B981',
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationDetailContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationDetaillable: {
    marginLeft: 4,
    fontSize: 14,
    color: themedColors.textSecondary,
    fontWeight: '600',
  },
  locationDetailText: {
    marginLeft: 4,
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    justifyContent: 'space-around',
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
  },
  statCard: {
    alignItems: 'center',
    padding: 12,
    backgroundColor: themedColors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
    width: '45%',
  },

  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.text,
    marginVertical: 8,
  },
  statLabel: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: themedColors.primary,
  },
  tabButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.textSecondary,
  },
  activeTabText: {
    color: themedColors.primary,
    fontWeight: 'bold',
  },
  tabContentContainer: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  listContent: {
    paddingBottom: 80, // Space for the floating button
  },
  addButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: themedColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  errorText: {
    fontSize: 16,
    color: themedColors.error,
    textAlign: 'center',
    marginTop: 20,
  },
  staffCard: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
    flexDirection: 'row',
    alignItems: 'center',
  },
  staffAvatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  staffAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  staffAvatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  staffAvatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.textSecondary,
  },
  staffStatusIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#10B981', // Green for active status
    borderWidth: 2,
    borderColor: themedColors.card,
  },
  staffInfo: {
    flex: 1,
    marginRight: 8,
  },
  staffNameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  staffName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  staffRoleBadge: {
    backgroundColor: '#EBF5FF', // Light blue background
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  staffRoleBadgeText: {
    fontSize: 12,
    color: '#3B82F6', // Blue text
    fontWeight: '500',
  },
  staffDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  staffDetailIcon: {
    marginRight: 6,
  },
  staffDetail: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  expensesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text
  },
  viewAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: themedColors.primary + '20',
  },
  viewAllText: {
    fontSize: 14,
    color: themedColors.primary,
    fontWeight: '600',
  },
  chartContainer: {
    alignItems: 'center',
    marginVertical: 10,
    backgroundColor: themedColors.isDarkMode ? themedColors.background : '#fff',  // Sherry    borderRadius: 16,
    padding: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    width: '100%', // Full width

  },
  expenseSummary: {
    marginTop: 20,
    backgroundColor: themedColors.isDarkMode ? themedColors.background : '#fff',  // Sherry
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    width: '100%', // Full width
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: themedColors.text
  },
  expenseCategoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1, // Use themed light border
    borderBottomColor: themedColors.borderLight,
  },
  urduExpenseCategoryItem: {
    flexDirection: 'row-reverse',
  },
  categoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  categoryName: {
    flex: 1,
    fontSize: 14,
    color: themedColors.text,
  },
  categoryAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: themedColors.text,
    paddingRight: 8, // Add padding to prevent text from being cut off
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: themedColors.border,
    paddingRight: 8, // Add padding to prevent text from being cut off
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.primary,
    maxWidth: '50%', // Limit width to prevent overflow
  },
  urduText: {
    // fontFamily: 'YourUrduFont', // Consider using a specific Urdu font
    textAlign: 'right',
    marginRight: 7
  },
  // Milking-specific styles
  milkingCard: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  urduCard: {
    alignItems: 'flex-end',
  },
  milkingCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  milkingAnimalInfo: {
    flex: 1,
  },
  milkingAnimalName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 4,
  },
  milkingDate: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  qualityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  qualityBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  milkingCardBody: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  milkingQuantity: {
    alignItems: 'center',
  },
  quantityValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.primary,
  },
  quantityUnit: {
    fontSize: 12,
    color: themedColors.textSecondary,
    marginTop: 2,
  },
  milkingNotes: {
    flex: 1,
    marginLeft: 16,
  },
  notesText: {
    fontSize: 14,
    color: themedColors.textSecondary,
    fontStyle: 'italic',
  },
  milkingStatsContainer: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  milkingStatsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  milkingStatsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  milkingStatsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  milkingStatCard: {
    alignItems: 'center',
    padding: 12,
    backgroundColor: themedColors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
    width: '45%',
  },
  milkingStatValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: themedColors.text,
    marginVertical: 8,
  },
  milkingStatLabel: {
    fontSize: 12,
    color: themedColors.textSecondary,
    textAlign: 'center',
  },
  milkingChartContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  milkingChartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  milkingRecordsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  milkingRecordsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  milkingViewAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: themedColors.primary + '20',
  },
  milkingViewAllText: {
    fontSize: 14,
    color: themedColors.primary,
    fontWeight: '600',
  },
  milkingRetryButton: {
    backgroundColor: themedColors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignSelf: 'center',
    marginTop: 16,
  },
  milkingRetryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  milkingRecordsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
});
