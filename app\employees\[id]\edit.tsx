import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  Image,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuthStore } from '@/store/auth-store';
import { User, Calendar, Mail, Phone, CreditCard, UserCircle, Save, Shield, Image as ImageIcon, ArrowLeft } from 'lucide-react-native';
import LoadingIndicator from '@/components/LoadingIndicator';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { getEmployeeById, updateEmployee, Employee } from '@/services/employee-service';

export default function EditEmployeeScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const id = params.id as string;
  const { t, language } = useTranslation();
  const { user } = useAuthStore();
  
  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [cnic, setCnic] = useState('');
  const [age, setAge] = useState('');
  const [gender, setGender] = useState('male');
  const [role, setRole] = useState('caretaker');
  const [joiningDate, setJoiningDate] = useState(new Date());
  const [photo, setPhoto] = useState<string | undefined>('');
  
  // UI state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [employee, setEmployee] = useState<Employee | null>(null);
  
  // Load employee data
  useEffect(() => {
    const fetchEmployeeData = async () => {
      try {
        if (id) {
          const employeeData = await getEmployeeById(id);
          if (employeeData) {
            setEmployee(employeeData);
            
            // Populate form fields
            setName(employeeData.name || '');
            setEmail(employeeData.email || '');
            setPhone(employeeData.phone_number || '');
            setCnic(employeeData.cnic || '');
            setAge(employeeData.age ? employeeData.age.toString() : '');
            setGender(employeeData.gender || 'male');
            setRole(employeeData.role || 'caretaker');
            setPhoto(employeeData.photo || undefined);
            
            // Set joining date if available
            if (employeeData.joining_date) {
              let timestamp: number;
              // Check if it's a Firestore timestamp
              if (typeof employeeData.joining_date === 'object' && 
                  'seconds' in employeeData.joining_date && 
                  typeof employeeData.joining_date.seconds === 'number') {
                timestamp = employeeData.joining_date.seconds * 1000;
              } else if (employeeData.joining_date instanceof Date) {
                timestamp = employeeData.joining_date.getTime();
              } else if (typeof employeeData.joining_date === 'number') {
                timestamp = employeeData.joining_date;
              } else {
                timestamp = Date.now();
              }
              setJoiningDate(new Date(timestamp));
            }
          }
        }
      } catch (error) {
        console.error('Error fetching employee:', error);
        Alert.alert(t('common.error'), t('farms.staffSection.fetchError'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmployeeData();
  }, [id]);
  
  const pickImage = async () => {
    // Image picking logic (same as add screen)
    // This would be implemented the same way as in the add screen
    Alert.alert('Feature coming soon', 'Image picking will be available in a future update');
  };
  
  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setJoiningDate(selectedDate);
    }
  };
  
  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    // Validate name
    if (!name.trim()) {
      errors.name = 'Name is required';
    }
    
    // Validate photo (optional for edit)
    // We don't require photo for editing
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  const handleSave = async () => {
    console.log('Save button pressed');

    if (!validateForm()) {
      console.log('Validation failed - missing required fields');
      console.log('Validation errors:', validationErrors);

      // Show a general validation error alert
      Alert.alert(
        t('common.validationError'),
        'Please fill in all required fields correctly.',
        [{ text: t('common.ok') }]
      );
      return;
    }

    if (!user?.id || !employee) {
      console.log('User not logged in or employee not found');
      Alert.alert(t('common.error'), 'You must be logged in to edit an employee');
      return;
    }
    
    try {
      setIsLoading(true);
      
      const updatedEmployee: Partial<Employee> = {
        name,
        email,
        phone_number: phone,
        cnic,
        age: parseInt(age) || 0,
        gender,
        role,
        joining_date: joiningDate,
        photo,
        updated_at: new Date(),
        // Remove updatedBy as it's not in the Employee interface
      };
      
      await updateEmployee(employee.id!, updatedEmployee);
      
      Alert.alert(
        t('common.success'),
        t('farms.staffSection.updateSuccess'),
        [
          {
            text: t('common.ok'),
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      console.error('Error updating employee:', error);
      Alert.alert(t('common.error'), t('farms.staffSection.updateError'));
    } finally {
      setIsLoading(false);
    }
  };
  
  if (isLoading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }
  
  if (!employee) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{t('farms.staffSection.notFound')}</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('farms.staffSection.editEmployee'),
        headerTitleStyle: { fontWeight: 'bold' },
        headerStyle: {
          backgroundColor: '#F9FAFB',
        },
        headerTintColor: colors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 8 }}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
        )
      }} />
      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          {/* Photo */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Photo*</Text>
            <TouchableOpacity onPress={pickImage} style={styles.photoContainer}>
              {photo ? (
                <Image source={{ uri: photo }} style={styles.photo} />
              ) : (
                <View style={[styles.photoPlaceholder, validationErrors.photo ? styles.inputError : null]}>
                  <ImageIcon size={40} color={validationErrors.photo ? colors.error : colors.textSecondary} />
                  <Text style={[styles.photoText, validationErrors.photo ? styles.errorText : null]}>Tap to add photo*</Text>
                </View>
              )}
            </TouchableOpacity>
            {validationErrors.photo && (
              <Text style={styles.errorText}>{validationErrors.photo}</Text>
            )}
          </View>

          {/* Name */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('farms.staffSection.name')}*</Text>
            <View style={styles.inputContainer}>
              <User size={20} color={colors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={[styles.input, validationErrors.name ? styles.inputError : null]}
                placeholder={t('farms.staffSection.namePlaceholder')}
                value={name}
                onChangeText={(text) => {
                  setName(text);
                  if (validationErrors.name) {
                    setValidationErrors((prev) => {
                      const newErrors = { ...prev };
                      delete newErrors.name;
                      return newErrors;
                    });
                  }
                }}
              />
            </View>
            {validationErrors.name && (
              <Text style={styles.errorText}>{validationErrors.name}</Text>
            )}
          </View>

          {/* Email */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('common.email')}</Text>
            <View style={styles.inputContainer}>
              <Mail size={20} color={colors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder={t('common.enterEmail')}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
              />
            </View>
          </View>

          {/* Phone */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('common.phone')}</Text>
            <View style={styles.inputContainer}>
              <Phone size={20} color={colors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder={t('common.enterPhone')}
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
              />
            </View>
          </View>

          {/* CNIC */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>CNIC</Text>
            <View style={styles.inputContainer}>
              <CreditCard size={20} color={colors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder={t('common.enterCNIC')}
                value={cnic}
                onChangeText={setCnic}
              />
            </View>
          </View>

          {/* Age */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('common.age')}</Text>
            <View style={styles.inputContainer}>
              <UserCircle size={20} color={colors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder={t('common.enterAge')}
                value={age}
                onChangeText={setAge}
                keyboardType="number-pad"
              />
            </View>
          </View>

          {/* Gender */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('common.gender')}</Text>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[styles.radioOption, gender === 'male' && styles.radioOptionSelected]}
                onPress={() => setGender('male')}
              >
                <Text style={[styles.radioText, gender === 'male' && styles.radioTextSelected]}>
                  {t('common.male')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.radioOption, gender === 'female' && styles.radioOptionSelected]}
                onPress={() => setGender('female')}
              >
                <Text style={[styles.radioText, gender === 'female' && styles.radioTextSelected]}>
                  {t('common.female')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Role */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('common.role')}</Text>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[styles.radioOption, role === 'caretaker' && styles.radioOptionSelected]}
                onPress={() => setRole('caretaker')}
              >
                <Shield size={16} color={role === 'caretaker' ? 'white' : colors.textSecondary} style={{ marginRight: 8 }} />
                <Text style={[styles.radioText, role === 'caretaker' && styles.radioTextSelected]}>
                  {t('common.caretaker')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.radioOption, role === 'manager' && styles.radioOptionSelected]}
                onPress={() => setRole('manager')}
              >
                <Shield size={16} color={role === 'manager' ? 'white' : colors.textSecondary} style={{ marginRight: 8 }} />
                <Text style={[styles.radioText, role === 'manager' && styles.radioTextSelected]}>
                  {t('common.manager')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Joining Date */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('farms.staffSection.joiningDate')}</Text>
            <TouchableOpacity
              style={styles.inputContainer}
              onPress={() => setShowDatePicker(true)}
            >
              <Calendar size={20} color={colors.textSecondary} style={styles.inputIcon} />
              <Text style={styles.dateText}>
                {format(joiningDate, 'MMM d, yyyy')}
              </Text>
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={joiningDate}
                mode="date"
                display="default"
                onChange={onDateChange}
              />
            )}
          </View>

          {/* Save Button */}
          <TouchableOpacity
            style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={isLoading}
          >
            {isLoading ? (
              <LoadingIndicator size="small" color="white" />
            ) : (
              <>
                <Save size={20} color="white" />
                <Text style={styles.saveButtonText}>{t('common.save')}</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: colors.text,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.card,
  },
  inputIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    height: 48,
    color: colors.text,
  },
  dateText: {
    flex: 1,
    height: 48,
    textAlignVertical: 'center',
    color: colors.text,
  },
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  radioOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.card,
  },
  radioOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  radioText: {
    color: colors.text,
    fontWeight: '500',
  },
  radioTextSelected: {
    color: 'white',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 14,
    borderRadius: 8,
    marginTop: 24,
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
  photoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  photo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 8,
  },
  photoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  photoText: {
    marginTop: 8,
    fontSize: 14,
    color: colors.textSecondary,
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    color: colors.error,
    fontSize: 12,
    marginTop: 4,
  },
});




