import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { FarmStatus } from '@/types/farm';
import { useTranslation } from '@/hooks/useTranslation';
import { Save, MapPin, Building2 } from 'lucide-react-native';
import Input from '@/components/Input';
import Button from '@/components/Button';
import { useToast } from '@/contexts/ToastContext';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
export default function AddFarmScreen() {
  const { t, language } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();
  const { addFarm, isLoading } = useFarmStore();
  const { showToast } = useToast();
  const themedColors = useThemeColors(); // Use the theme hook

  // Immediate check for caretaker role - TRIPLE PROTECTION
  if (!user || !(user.role === 'owner' || user.role === 'admin')) {
    console.log('AddFarmScreen - Unauthorized role detected:', user?.role);
    // Use setTimeout to ensure this runs after component mount
    setTimeout(() => {
      router.replace('/(tabs)');
      Alert.alert(t('common.permissionDenied'), t('common.noAccessToThisFeature'));
    }, 0);
    // Return empty component while redirecting
    return null;
  }

  const styles = getStyles(themedColors, language); // Generate styles with themed colors

  const [name, setName] = useState('');
  const [location, setLocation] = useState('');
  const [status, setStatus] = useState<FarmStatus>(FarmStatus.ACTIVE);
  const [errors, setErrors] = useState({
    name: '',
    location: '',
  });

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: '',
      location: '',
    };

    if (!name.trim()) {
      newErrors.name = t('farms.nameRequired');
      isValid = false;
    }

    if (!location.trim()) {
      newErrors.location = t('farms.locationRequired');
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // const handleSave = async () => {
  //   if (!validateForm()) return;
  //   if (!user) return;

  //   try {
  //     const newFarm = await addFarm({
  //       name,
  //       location,
  //       status,
  //       ownerId: user.id,
  //       animalCount: 0,
  //       staffCount: 0,
  //     });

  //     showToast({
  //       type: 'success',
  //       title: t('common.success'),
  //       message: t('farms.addSuccess'),
  //     });

  //     // Navigate to the farm detail screen
  //     router.replace(`/farms/${newFarm.id}`);
  //   } catch (error) {
  //     console.error('Error adding farm:', error);
  //     showToast({
  //       type: 'error',
  //       title: t('common.error'),
  //       message: t('farms.addError'),
  //     });
  //   }
  // };
  const handleSave = async () => {
    if (!validateForm()) return;
    if (!user) return;
  
    try {
      // Add the farm to Firestore
      const newFarm = await addFarm({
        name,
        location,
        status,
        ownerId: user.id,
        animalCount: 0,
        staffCount: 0,
      });
  
      // Update the owner's ownedFarms array
      try {
        const userRef = doc(firestore, 'users', user.id);
        const userDoc = await getDoc(userRef);
        
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const ownedFarms = userData.ownedFarms || [];
          
          // Add the new farm ID to the ownedFarms array if it's not already there
          if (!ownedFarms.includes(newFarm.id)) {
            await updateDoc(userRef, {
              ownedFarms: [...ownedFarms, newFarm.id]
            });
            console.log(`Updated user ${user.id} with new farm ${newFarm.id}`);
          }
        }
      } catch (userUpdateError) {
        console.error('Error updating user ownedFarms:', userUpdateError);
        // Continue even if user update fails
      }
  
      showToast({
        type: 'success',
        title: t('common.success'),
        message: t('farms.addSuccess'),
      });
  
      // Navigate to the farm detail screen
      router.replace(`/farms/${newFarm.id}`);
    } catch (error) {
      console.error('Error adding farm:', error);
      showToast({
        type: 'error',
        title: t('common.error'),
        message: t('farms.addError'),
      });
    }
  };
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('farms.addFarm'),
          headerBackTitle: t('common.back'),
        }}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.name')}*</Text>
            </View>
            <Input
              value={name}
              onChangeText={setName}
              placeholder={t('farms.namePlaceholder')}
              error={errors.name} // @ts-ignore
              leftIcon={<Building2 size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.location')}*</Text>
            </View>
            <Input
              value={location}
              onChangeText={setLocation}
              placeholder={t('farms.locationPlaceholder')}
              error={errors.location} // @ts-ignore
              leftIcon={<MapPin size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.status')}</Text>
            </View>
            <View style={[
              styles.statusButtons,
              language === 'ur' && { flexDirection: 'row-reverse' }
            ]}>
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.ACTIVE && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.ACTIVE)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.ACTIVE && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusActive')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.INACTIVE && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.INACTIVE)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.INACTIVE && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusInactive')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.PENDING && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.PENDING)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.PENDING && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusPending')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t('farms.saveFarm')}
              onPress={handleSave}
              isLoading={isLoading}
              leftIcon={<Save size={20} color="white" />}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
    paddingTop: 24, // Add some top padding for better spacing
  },
  formGroup: {
    marginBottom: 20, // Increased margin for better separation
  },
  labelContainer: {
    marginBottom: 8,
    flexDirection: language === 'ur' ? 'row-reverse' : 'row', // Adjust for Urdu
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    textAlign: language === 'ur' ? 'right' : 'left', // Adjust for Urdu
  },
  statusButtons: {
    flexDirection: 'row',
    justifyContent: language === 'ur' ? 'flex-end' : 'flex-start', // Adjust for Urdu
    flexWrap: 'wrap',
  },
  statusButton: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: language === 'ur' ? 0 : 8, // Adjust for Urdu
    marginLeft: language === 'ur' ? 8 : 0, // Adjust for Urdu
    marginBottom: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  statusButtonSelected: {
    backgroundColor: themedColors.primary,
    borderColor: themedColors.primary,
  },
  statusButtonText: {
    color: themedColors.text,
    fontSize: 14,
    fontWeight: '500',
  },
  statusButtonTextSelected: {
    color: 'white',
  },
  buttonContainer: {
    marginTop: 32, // Increased margin for the button
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
});
