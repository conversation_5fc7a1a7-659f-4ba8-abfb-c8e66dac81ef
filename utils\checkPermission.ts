import { doc, getDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { User } from '@/types/user';
import { Permission } from '@/hooks/usePermissions';

/**
 * Check if a user has permission to access a specific farm
 *
 * @param userId ID of the user to check
 * @param farmId ID of the farm to check access for
 * @returns Promise resolving to a boolean indicating if the user has access
 */
export async function checkFarmAccess(userId: string, farmId: string): Promise<boolean> {
  try {
    // Get the user
    const userRef = doc(firestore, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    const userData = userDoc.data() as User;

    // Get the farm
    const farmRef = doc(firestore, 'farms', farmId);
    const farmDoc = await getDoc(farmRef);

    if (!farmDoc.exists()) {
      return false;
    }

    const farmData = farmDoc.data();

    // Check access based on role
    if (userData.role === 'owner') {
      // Owners can access their own farms
      return farmData.ownerId === userId;
    } else if (userData.role === 'admin' || userData.role === 'caretaker') {
      // Admins and caretakers can access farms they're assigned to
      return userData.assignedFarms?.includes(farmId) || false;
    }

    return false;
  } catch (error) {
    console.error('Error checking farm access:', error);
    return false;
  }
}

/**
 * Check if a user has permission to access a specific animal
 *
 * @param userId ID of the user to check
 * @param animalId ID of the animal to check access for
 * @returns Promise resolving to a boolean indicating if the user has access
 */
export async function checkAnimalAccess(userId: string, animalId: string): Promise<boolean> {
  try {
    // Get the animal
    const animalRef = doc(firestore, 'animals', animalId);
    const animalDoc = await getDoc(animalRef);

    if (!animalDoc.exists()) {
      return false;
    }

    const animalData = animalDoc.data();

    // Check if the animal belongs to a farm
    if (animalData.formId) {
      // Check farm access
      return checkFarmAccess(userId, animalData.formId);
    }

    // Check if the user is the owner of the animal
    return animalData.ownerId === userId || animalData.tenantId === userId;
  } catch (error) {
    console.error('Error checking animal access:', error);
    return false;
  }
}

/**
 * Check if a user has permission to access a specific health record
 *
 * @param userId ID of the user to check
 * @param recordId ID of the health record to check access for
 * @returns Promise resolving to a boolean indicating if the user has access
 */
export async function checkHealthRecordAccess(userId: string, recordId: string): Promise<boolean> {
  try {
    // Get the health record
    const recordRef = doc(firestore, 'healthChecks', recordId);
    const recordDoc = await getDoc(recordRef);

    if (!recordDoc.exists()) {
      return false;
    }

    const recordData = recordDoc.data();

    // Check if the user has access to the animal
    return checkAnimalAccess(userId, recordData.animalId);
  } catch (error) {
    console.error('Error checking health record access:', error);
    return false;
  }
}

/**
 * Check if a user has permission to perform a specific action on a resource
 *
 * @param userId ID of the user to check
 * @param permission The permission to check
 * @param resourceId ID of the resource to check permission for
 * @returns Promise resolving to a boolean indicating if the user has permission
 */
export async function checkPermission(
  userId: string,
  permission: Permission,
  resourceId?: string
): Promise<boolean> {
  try {
    // Get the user
    const userRef = doc(firestore, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    const userData = userDoc.data() as User;

    // If no resource ID is provided, check general permission based on role
    if (!resourceId) {
      switch (userData.role) {
        case 'owner':
          // Owners have all permissions
          return true;

        case 'admin':
          // Admins have most permissions except those specific to owners
          return permission !== Permission.CREATE_FARM &&
                 permission !== Permission.DELETE_FARM &&
                 permission !== Permission.INVITE_ADMIN;

        case 'caretaker':
          // Caretakers have limited permissions
          return permission === Permission.VIEW_FARM ||
                 permission === Permission.VIEW_ANIMAL ||
                 permission === Permission.VIEW_STAFF ||
                 permission === Permission.VIEW_TASK ||
                 permission === Permission.VIEW_HEALTH_RECORD ||
                 permission === Permission.CREATE_HEALTH_RECORD;

        default:
          return false;
      }
    }

    // Check permission for a specific resource
    if (permission.toString().includes('FARM')) {
      return checkFarmAccess(userId, resourceId);
    } else if (permission.toString().includes('ANIMAL')) {
      return checkAnimalAccess(userId, resourceId);
    } else if (permission.toString().includes('HEALTH_RECORD')) {
      return checkHealthRecordAccess(userId, resourceId);
    }

    return false;
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
}
